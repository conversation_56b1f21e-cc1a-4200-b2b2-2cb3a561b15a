# frozen_string_literal: true

module Api
  # app/controllers/email_templates_controller.rb
  class EmailTemplatesController < Api::BaseController
    include Api::ApiRescues

    def index
      templates = EmailTemplate.all.order('id ASC')
      render_success(data: templates, options: { params: { action_name: 'index' } })
    end

    def show
      template = EmailTemplate.find(params[:id])
      render_success(data: template, options: { params: { action_name: 'show' } })
    end

    def preview
      template = EmailTemplate.find(params[:id])
      dynamic_fields = params[:dynamic_fields] || {}
      html = template.generate_preview(dynamic_fields)

      render_success(data: { preview_html: html })
    end

    def email_preview_file_uploads_s3
      file = email_preview_file_upload_params[:file]
      filename = file.original_filename
      url = ImageUrlService.new.generate_url_with_file(file)
      object_hash = { file: { name: filename, url: url } }
      render_success(data: { attributes: object_hash })
    end

    def delete_email_preview_file
      file_url = email_preview_file_upload_params[:url]
      ImageUrlService.delete_object(file_url)
      render_success(message: 'Image deleted successfully')
    end

    private

    def email_template_params
      params.permit(:name, :sendgrid_template_id)
    end

    def email_preview_file_upload_params
      params.require(:email_preview_file_uploads).permit(:file, :field_name, :url)
    end

    def serialize_template(template)
      {
        id: template.id,
        template_name: template.name
      }
    end
  end
end
