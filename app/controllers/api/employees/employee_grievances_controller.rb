# frozen_string_literal: true

module Api
  module Employees
    class EmployeeGrievancesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_grievances, only: %w[edit update destroy]
      before_action :normalize_employee_ids_param, only: [:create, :update]

      def index
        employee_grievances = if params[:employee_id].blank? || params[:employee_id] == 'null'
                                EmployeeGrievance.includes(:grievance, :office, :grievance_status, :poly_notes, employee_grievance_steps: [:files_attachments], files_attachments: :blob)
                                                 .kept.where(employee_id: nil)
                              else
                                EmployeeGrievance.includes(:grievance, :office, :grievance_status, :poly_notes, employee_grievance_steps: [:files_attachments], files_attachments: :blob)
                                                 .kept.where('employee_id = :employee_id OR (employee_id IS NULL AND :employee_id = ANY(employee_ids))', employee_id: params[:employee_id])
                              end
        employee_grievances = employee_grievances.where(number: params[:search_number]) if params[:search_number].present?

        if (start_date = params[:search_date_from]).present? && (end_date = params[:search_date_to]).present?
          employee_grievances = search_with_dates(start_date, end_date, employee_grievances)
        end

        employee_grievances = employee_grievances.where(status: params[:status]) if params[:status].present?

        employee_grievances = search_by_employee_grievance(params[:search_text], employee_grievances) if params[:search_text].present?

        employee_grievances = if current_account.saas_json.dig('schema', 'employee_grievances', 'grievances_specific_logics_for_papba') == true
                                employee_grievances.order(Arel.sql("CASE WHEN number ~ '^[0-9]+' THEN regexp_replace(number, '^([0-9]+).*$', '\\1')::int
                                                                    ELSE NULL END DESC NULLS LAST, number DESC, date DESC NULLS LAST"))
                              else
                                employee_grievances.order('id DESC')
                              end

        pagy, employee_grievances = pagy(employee_grievances, items: params[:per_page])
        render_success(data: employee_grievances, options: { change_request: %w[employee_grievance employee_grievance_step],
                                                             meta: pagy_headers_hash(pagy), include: %i[employee_grievance_steps poly_notes] })
      end

      def get_max_case_number
        number = EmployeeGrievance.select(:number).maximum(:number)
        response_data = number.present? ? { number: (number.to_i + 1).to_s } : { number: '202350' }
        render_success(data: response_data)
      end

      def create
        employee_grievance = EmployeeGrievance.new(resource_params)
        employee_grievance.current_user = current_user
        employee_grievance.save
        render_json(data: employee_grievance, options: { include: [:poly_notes] })
      end

      def update
        @employee_grievance.case_number = resource_params[:number]
        poly_notes_to_delete = params[:employee_grievance][:delete_poly_notes_id]&.split(',')
        PolyNote.where(id: poly_notes_to_delete).destroy_all if poly_notes_to_delete.present?
        @employee_grievance.current_user = current_user
        @employee_grievance.update(resource_params)
        render_json(data: @employee_grievance, options: { include: [:poly_notes] })
      end

      def destroy
        @employee_grievance.discard
        @employee_grievance.files.purge_later
        render_json(data: @employee_grievance)
      end

      private

      def resource_params
        resource_param = params.require(:employee_grievance).permit(:description, :employee_id, :grievance_id, :date, :charge, :number, :grievance_status_id,
                                                                    :violation_alleged, :status, :provisions, :office_id, :arbitrator_id, :pa_attorney_id,
                                                                    poly_notes_attributes: %i[notable_id notes notable_type id], employee_ids: [], files: [])
        resource_param[:employee_id] = nil if resource_param[:employee_id] == 'null'
        resource_param[:files] = [] if params[:employee_grievance][:remove_all_files] == 'true'
        resource_param
      end

      def set_employee_grievances
        @employee_grievance = EmployeeGrievance.kept.includes(:grievance, files_attachments: :blob).find(params[:id])
      end

      def search_with_dates(from_date, to_date, employee_grievances)
        conditions = []
        conditions << "DATE(date) >= '#{from_date}'" if from_date.present?
        conditions << "DATE(date) <= '#{to_date}'" if to_date.present?

        employee_grievances.where(conditions.join(' AND '))
      end

      def search_by_employee_grievance(query, employee_grievances)
        employee_ids = Employee.unscoped.where("REGEXP_REPLACE(CONCAT_WS(' ', COALESCE(first_name, ''), COALESCE(middle_name, ''), COALESCE(last_name, ''), COALESCE(suffix, '')), '\\s+', ' ', 'g') ILIKE ?",
                                               "%#{query}%").pluck(:id)
        employee_grievances.left_joins(:office, :grievance).where(
          "number = :query_id OR grievances.name ILIKE :query OR employee_id IN (:ids) OR arbitrator_id IN (:ids) OR pa_attorney_id IN (:ids)
          OR offices.name ILIKE :query OR employee_ids && ARRAY[:ids]::text[]", query_id: query, ids: employee_ids, query: "%#{query}%"
        )
      end

      def normalize_employee_ids_param
        employee_ids = params[:employee_grievance][:employee_ids]
        return if employee_ids.blank? || employee_ids.is_a?(Array)

        params[:employee_grievance][:employee_ids] = employee_ids.split(',').map(&:to_i)
      end
    end
  end
end
