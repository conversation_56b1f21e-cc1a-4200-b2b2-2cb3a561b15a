class SendgridNotificationRetryJob < ApplicationJob
  queue_as :default
  sidekiq_options retry: 3

  def perform(notification_id:, recipients: [], dynamic_contents: {}, template_id:, sender_address: nil, current_tenant: nil, retrying_job: nil)

    sendgrid_service = SendgridService.new
    service_return = sendgrid_service.send_bulk_email(
      notification_id: notification_id,
      recipients: recipients,
      dynamic_contents: dynamic_contents,
      template_id: template_id,
      sender_address: sender_address,
      current_tenant: current_tenant,
      retrying_job: retrying_job
    )
    if service_return.class != TrueClass
      CustomAppsignalError.send_error(e, Apartment::Tenant.current)
      raise service_return
    end
  end
end