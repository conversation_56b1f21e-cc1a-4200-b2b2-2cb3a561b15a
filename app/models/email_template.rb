# frozen_string_literal: true

class EmailTemplate < ApplicationRecord

  ## Validations
  validates :template_id, :name, presence: true, uniqueness: true

  ## Callbacks
  before_create :set_html_and_dynamic_fields

  def update_template
    set_html_and_dynamic_fields
    save!
  end

  def generate_preview(dynamic_fields = {})
    return '' if template_html.nil? || template_html.empty?

    fields = dynamic_fields.transform_keys(&:to_s)
    template = template_html.dup

    # Robust nested conditional parser
    def parse_conditionals(template, fields)
      result = ''
      i = 0

      while i < template.length
        if template[i..].start_with?('{{#if ')
          # Match start tag
          if_match = template[i..].match(/\A\{\{#if\s+(\w+)\s*\}\}/)
          field = if_match[1]
          i += if_match[0].length

          # Locate matching {{/if}}
          depth = 1
          block_start = i
          while i < template.length
            if template[i..].match(/\A\{\{#if\s+(\w+)\s*\}\}/)
              depth += 1
              i += Regexp.last_match[0].length
            elsif template[i..].start_with?('{{/if}}')
              depth -= 1
              i += '{{/if}}'.length
              break if depth == 0
            else
              i += 1
            end
          end

          block = template[block_start...(i - '{{/if}}'.length)]

          if fields[field] && fields[field].to_s.strip != ''
            result += parse_conditionals(block, fields)
          end
        else
          result += template[i]
          i += 1
        end
      end

      result
    end

    # Step 1: Handle nested conditionals
    rendered = parse_conditionals(template, fields)

    # Step 2: Replace raw HTML variables {{{field}}}
    rendered = rendered.gsub(/\{\{\{(\w+)\}\}\}/) do
      fields[$1].to_s
    end

    # Step 3: Replace normal variables {{field}}
    rendered = rendered.gsub(/\{\{(\w+)\}\}/) do
      fields[$1].to_s
    end

    # Step 4: Remove unnecessary whitespace
    rendered.gsub(/[\n\r\t]+/, ' ').gsub(/\s{2,}/, ' ').strip
  end


  private

  def set_html_and_dynamic_fields
    if (self.template_html = SendgridService.new.fetch_template(template_id)).blank?
      errors.add(:template_id, 'No template found with this template ID')
      return
    end

    self.dynamic_fields = extract_dynamic_fields
  end

  def extract_dynamic_fields
    return unless template_html.present?

    # Get dynamic fields
    matches = template_html.scan(/\{\{([^{}]+)\}\}/).flatten.uniq

    # Filter conditional fields
    filtered_matches = matches.reject do |field|
      field.start_with?('#if') || field.start_with?('/if') || field.start_with?('{unsubscribe')
    end

    field_data = {}
    filtered_matches.each do |field|
      field_name = field.strip

      field_type = if field_name.include?('image')
                     'image'
                   elsif field_name.include?('url')
                     'url'
                   elsif field_name.include?('text')
                     'text'
                   else
                     'string'
                   end

      field_data[field_name] = { "type": field_type, "placeholder": field_name.humanize }
    end

    field_data
  end
end
