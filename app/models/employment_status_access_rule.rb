# frozen_string_literal: true

class EmploymentStatusAccessRule < ApplicationRecord
  # ============================= Associations =========================================================================
  belongs_to :viewer_employment_status, class_name: 'EmploymentStatus'
  belongs_to :viewable_employment_status, class_name: 'EmploymentStatus'

  # ============================= Validations ==========================================================================
  validates :viewer_employment_status_id, uniqueness: { 
    scope: :viewable_employment_status_id,
    message: 'Access rule already exists for this combination'
  }
  validates :viewer_employment_status_id, :viewable_employment_status_id, presence: true

  # ============================= Scopes ===============================================================================
  scope :for_viewer_status, ->(employment_status_id) { where(viewer_employment_status_id: employment_status_id) }
  scope :for_viewable_status, ->(employment_status_id) { where(viewable_employment_status_id: employment_status_id) }

  # ============================= Class Methods ========================================================================
  
  # Get all employment status IDs that a given employment status can view
  def self.viewable_status_ids_for(viewer_employment_status_id)
    return [] if viewer_employment_status_id.blank?
    
    for_viewer_status(viewer_employment_status_id).pluck(:viewable_employment_status_id)
  end

  # Check if a viewer employment status can view a specific employment status
  def self.can_view?(viewer_employment_status_id, viewable_employment_status_id)
    return false if viewer_employment_status_id.blank? || viewable_employment_status_id.blank?
    
    exists?(
      viewer_employment_status_id: viewer_employment_status_id,
      viewable_employment_status_id: viewable_employment_status_id
    )
  end

  # Get all employment statuses that can view a specific employment status
  def self.viewer_status_ids_for(viewable_employment_status_id)
    return [] if viewable_employment_status_id.blank?
    
    for_viewable_status(viewable_employment_status_id).pluck(:viewer_employment_status_id)
  end

  # Bulk create access rules for an employment status
  def self.create_access_rules_for_viewer(viewer_employment_status_id, viewable_employment_status_ids)
    return false if viewer_employment_status_id.blank? || viewable_employment_status_ids.blank?

    rules_to_create = viewable_employment_status_ids.map do |viewable_id|
      {
        viewer_employment_status_id: viewer_employment_status_id,
        viewable_employment_status_id: viewable_id,
        created_at: Time.current,
        updated_at: Time.current
      }
    end

    insert_all(rules_to_create, unique_by: [:viewer_employment_status_id, :viewable_employment_status_id])
  end

  # Remove access rules for an employment status
  def self.remove_access_rules_for_viewer(viewer_employment_status_id, viewable_employment_status_ids = nil)
    return false if viewer_employment_status_id.blank?

    query = for_viewer_status(viewer_employment_status_id)
    query = query.where(viewable_employment_status_id: viewable_employment_status_ids) if viewable_employment_status_ids.present?
    query.delete_all
  end
end
