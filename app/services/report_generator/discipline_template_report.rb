# frozen_string_literal: true

module ReportGenerator
  class DisciplineTemplateReport < ApplicationReport
    def generate
      report.format = report_format
      report.save!

      generate_pdf
      save_report
    end

    def generate_pdf
      return unless report_format == 'pdf'

      employee_discipline_setting = EmployeeDisciplineSetting.find(params[:employee_discipline_setting_id])
      ReportGenerator::Pdf::DisciplineDiscoveryLetterTemplatePdf.create(employee_discipline_setting, filename, filepath, current_account, params[:template_name])
    end

    private

    def report_type
      Report::ReportTypes::DISCIPLINE_TEMPLATE
    end

    def filename
      @filename ||= "discipline-#{params[:template_name]}-template-#{report.id}-#{report_created_at}.#{report_format}"
    end
  end
end
