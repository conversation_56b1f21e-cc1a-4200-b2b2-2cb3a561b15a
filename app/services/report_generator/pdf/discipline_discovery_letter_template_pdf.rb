# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class DisciplineDiscoveryLetterTemplatePdf < ApplicationPdf

      def self.create(object, filename, filepath, current_account, template_name)
        new(object, filename, filepath, current_account, template_name).create
      end

      def initialize(object, filename, filepath, current_account, template_name)
        @object = object
        @filename = filename
        @filepath = filepath
        @current_account = current_account
        @template_name = template_name
      end

      private

      attr_reader :template_name

      def pdf
        template = "templates/discipline_templates/#{template_name}_pdf"
        height = '14in'
        ac.render_to_string(
          encoding: 'UTF-8',
          locals: {
            employee_discipline_setting: object,
            employee: object&.employee
          },
          pdf: filename,
          template: template,
          layout: 'pdf.html',
          page_width: '8.5in',
          page_height: height,
          padding: {
            top: 30
          }
        )
      end
    end
  end
end
