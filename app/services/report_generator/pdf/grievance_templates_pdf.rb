# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class GrievanceTemplatesPdf < ApplicationPdf
      def self.create(object, filename, filepath, current_account, template_name)
        new(object, filename, filepath, current_account, template_name).create
      end

      def initialize(object, filename, filepath, current_account, template_name)
        @object = object
        @filename = filename
        @filepath = filepath
        @current_account = current_account
        @template_name = template_name
      end

      private

      attr_reader :template_name

      def pdf
        template = "templates/grievance_templates/#{template_name}_pdf"
        page_size = template_name == "grievance" ? 'Legal' : 'Letter' # Legal paper size = 8.5in x 14in, Letter paper size = 8.5in x 11in
        ac.render_to_string(
          encoding: 'UTF-8',
          locals: {
            employee_grievance: object
          },
          pdf: filename,
          template: template,
          layout: 'pdf.html',
          page_size: page_size,
          padding: {
            top: 30
          }
        )
      end
    end
  end
end
