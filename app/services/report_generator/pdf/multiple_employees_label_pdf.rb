# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class MultipleEmployeesLabelPdf < ApplicationPdf
      def self.create(object, filename, filepath, mailing_label = nil, columns = nil)
        mailing_label ||= 'CustomAvery5160'
        new(object, filename, filepath, mailing_label, columns).create
      end

      def initialize(object, filename, filepath, mailing_label, columns)
        @object = object
        @filename = filename
        @filepath = filepath
        @mailing_label = mailing_label
        @columns = columns
        @label_dimensions = label_dimension(mailing_label)
      end

      private

      attr_reader :mailing_label, :columns, :label_dimensions

      def pdf # rubocop:disable Metrics/AbcSize
        current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
        show_company = current_account.saas_json.dig('schema', 'contact_persons').present?
        company_value_key = current_account.saas_json.dig('schema', 'contact_persons')&.key('Company')&.to_sym if show_company
        custom_avery_label = current_account.saas_json['ui'].dig('reports', 'single_employee', 'avery_columns') if mailing_label == 'CustomAvery5366'

        Prawn::Labels.render(object.order_by_name, type: mailing_label) do |pdf, data|
          font_size = base_font_size(mailing_label)
          if %w[CustomAvery5160 CustomAvery5161 CustomAvery5735].include?(mailing_label)
            font_size -= 2 if data.apartment.present?
            adjusted_font_size = adjust_font_size(pdf, font_size, data, company_value_key)
          end
          pdf.font_size = adjusted_font_size || font_size

          if mailing_label == 'CustomAvery5366'
            pdf.text data.name.upcase, style: :bold
            pdf.text data.a_number, style: :bold if custom_avery_label.nil? || columns.include?('a_number')
            pdf.text data.rank_name, style: :bold if columns.present? && columns.include?('rank')
            pdf.text data.shield_number, style: :bold if columns.present? && columns.include?('shield_number')
            pdf.text data.placard_number, style: :bold if columns.present? && columns.include?('placard_number')
          else
            pdf.text '<u><b>TO:</b></u>', inline_format: true if mailing_label == 'CustomPostCard'
            pdf.text data.name.upcase
            pdf.text data[company_value_key] if show_company && data[company_value_key].present?
            pdf.text data.street
            pdf.text data.apartment if data.apartment.present?
            pdf.text data.employee_address, overflow: :shrink_to_fit
          end
        end
      end

      def base_font_size(label)
        font_sizes = { 'CustomAvery5366' => 14, 'CustomEnvelope10' => 14, 'CustomAvery5161' => 12, 'CustomPostCard' => 18, 'CustomAvery5160' => 12, 'CustomAvery5163' => 14, 'CustomAvery5735' => 12 }
        font_sizes[label] || 12
      end

      def label_dimension(label)
        dimensions = { 'CustomAvery5160' => { width: 189, height: 72 }, 'CustomAvery5161' => { width: 288, height: 72 }, 'CustomAvery5735' => { width: 216, height: 72 } }
        dimensions[label] || { width: 200, height: 100 }
      end

      def adjust_font_size(pdf, font_size, data, company_value_key)
        max_width = label_dimensions[:width] - 15 # For edge span
        max_height = label_dimensions[:height] - 15 # For edge span
        min_font_size = 8
        texts = [
          data.name.upcase,
          (data[company_value_key] if company_value_key && data[company_value_key].present?),
          data.street,
          (data.apartment if data.apartment.present?),
          data.employee_address
        ].compact

        while font_size > min_font_size
          text_content = texts.join("\n")
          break unless texts.any? { |text| pdf.width_of(text, size: font_size) >= max_width } || pdf.height_of(text_content, size: font_size) >= max_height

          font_size -= 1
        end

        font_size
      end
    end
  end
end
