class AddDisciplineColumnsToEmployeeDisciplineSettings < ActiveRecord::Migration[6.0]
  disable_ddl_transaction!

  def change
    add_column :employee_discipline_settings, :discipline_type, :integer, null: true
    add_column :employee_discipline_settings, :docket_number, :string
    add_column :employee_discipline_settings, :duty_status, :integer, null: true
    add_column :employee_discipline_settings, :arbitrator_id, :integer
    add_column :employee_discipline_settings, :pa_attorney_id, :integer
    add_column :employee_discipline_settings, :status, :integer


    add_reference :employee_discipline_settings, :office,
                  foreign_key: true,
                  index: { algorithm: :concurrently }
    add_reference :employee_discipline_settings, :discipline_status,
                  foreign_key: true,
                  index: { algorithm: :concurrently }
  end
end
