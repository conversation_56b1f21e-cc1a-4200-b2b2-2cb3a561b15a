# frozen_string_literal: true

require 'csv'
desc 'import Cell and Email'

task :nyccoba_cell_and_email_revert, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  Employee.kept.includes(:contacts).find_each do |employee|
    @row_number = "#{employee.first_name} #{employee.last_name}"

    contacts = employee.contacts
    personal_phone = contacts.select { |x| x.contact_for == 'personal' && x.contact_type == 'phone' }.first
    personal_email = contacts.select { |x| x.contact_for == 'personal' && x.contact_type == 'email' }.first
    work_phone = contacts.select { |x| x.contact_for == 'work' && x.contact_type == 'phone' }.first
    work_email = contacts.select { |x| x.contact_for == 'work' && x.contact_type == 'email' }.first

    [[personal_phone, work_phone], [personal_email, work_email]].each do |personal, work|
      work_versions_created = work&.versions&.pluck(:created_at)&.sort
      if work_versions_created.blank? || work_versions_created.last >= Date.parse('2024-04-26')
        personal.update!(value: '') if personal&.value&.present?
        next
      end

      work.update!(value: personal.value) if personal&.value&.present?
      personal.update!(value: '') if personal&.value&.present?

    rescue => e
      p @row_number, e.message
      nyccoba_feed_errors(e.message)
    end

    # Contact.import total_arr.flatten, on_duplicate_key_update: [:value]
  end

  CSV.open("#{Rails.root}/nyccoba_cell_and_email_revert_#{Time.new.strftime('%d-%m-%Y-%H:%M:%S')}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_terminated_dependents_update, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  member_id = nil
  employee_outside_loop = nil
  terminated_employees = csv_file.select { |row| row['Status']&.strip&.downcase == 'terminated' }

  terminated_employees.each do |row|
    ssn = row['SSN'] || ''
    first_name = row['FirstName']&.strip || ''
    last_name = row['LastName']&.strip || ''
    relationship = row['Relation']&.strip&.downcase&.gsub(' ', '_') || ''
    relationship_name = %w[son daughter].include?(relationship) ? 'child' : relationship
    dob = Date.parse(row['DOB']) || '' if row['DOB'].present?
    @row_number = first_name + ' ' + last_name
    if ssn.blank? && (first_name.blank? || last_name.blank?)
      nyccoba_feed_errors('Mandatory Details Missing')
      next
    end

    if ssn.present? && member_id != row['MemberID']
      ssn_rjust = ssn.gsub('-', '').rjust(9, '0')
      parsed_ssn = nyccoba_parse_ssn(ssn_rjust)
      employees = Employee.kept.where(social_security_number: parsed_ssn)
    end

    if employees&.count == 1 || member_id == row['MemberID']
      employee = if member_id.blank? || member_id != row['MemberID']
                   employees.first
                 elsif member_id == row['MemberID']
                   employee_outside_loop
                 end
      coverages = employee.benefit_coverages.where(first_name: first_name, last_name: last_name)
      coverages = coverages.where(birthday: dob) if dob.present?
      coverages = coverages.where(relationship: relationship_name) if relationship.present?
    else
      nyccoba_feed_errors('Employee not found') if employees.blank?
      nyccoba_feed_errors('More than One Employee Found') if (employees&.count || 0) > 1
      coverages = BenefitCoverage.kept.where(first_name: first_name, last_name: last_name)
      coverages = coverages.where(birthday: dob) if dob.present?
      coverages = coverages.where(relationship: relationship_name) if relationship_name.present?
    end

    if coverages.blank?
      member_feed_errors('No Dependents Found')
      next
    end
    coverages_to_update = coverages.where(expires_at: nil)
    member_feed_errors('Coverage Terminated Successfully') if coverages_to_update.present? && coverages_to_update.update_all(expires_at: Date.parse('1/3/2024'))

    employee_outside_loop = employee if employees.present?
    member_id = row['MemberID']
  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end
  generate_csv_report_errors('terminated_dependents_update')
end

task :nyccoba_employees_of_terminated_dependents, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  output_file_path = "#{Rails.root}/Employee_name_for_terminated_dependents_#{Time.current.strftime('%d-%m-%Y-%H:%M')}.csv"

  @errors = {}
  CSV.open(output_file_path, 'w') do |csv|
    csv << ['Coverage_name', 'Employee Name', 'Employment Status']
    csv_file.each do |row|
      @row_number = row['Coverage Name']&.strip
      next if @row_number.blank?

      name_split = @row_number.split(' ')
      name_queries = generate_name_conditions(name_split)
      if name_queries.blank?
        member_feed_errors('Invalid Coverage Name')
        next
      end
      coverages = BenefitCoverage.kept.where(name_queries[:conditions], *name_queries[:values]).where(expires_at: Date.parse('1/3/2024'))
      if coverages.blank?
        member_feed_errors('Coverage not Found')
        next
      end
      employee = coverages.first&.employee
      csv << [@row_number, employee&.full_name || '', employee&.employment_status_name || '']
    rescue StandardError => e
      p @row_number, e.message
      member_feed_errors(e.message)
    end
  end
  generate_csv_report_errors('terminated_dependents_with_members_')
end

def generate_name_conditions(name_split)
  values = []

  case name_split.length
  when 2
    conditions = '(first_name = ? AND last_name = ?)'
    values.push(name_split[0], name_split[1])
  when 3
    conditions = '(first_name = ? AND last_name = ?) OR (first_name = ? AND last_name = ?)'
    values.push("#{name_split[0]} #{name_split[1]}", name_split[2], name_split[0], "#{name_split[1]} #{name_split[2]}")
  when 4
    conditions = '(first_name = ? AND last_name = ?) OR (first_name = ? AND last_name = ?) OR (first_name = ? AND last_name = ?)'
    values.push("#{name_split[0]} #{name_split[1]}", "#{name_split[2]} #{name_split[3]}", "#{name_split[0]} #{name_split[1]} #{name_split[2]}",
                name_split[3], name_split[0], "#{name_split[1]} #{name_split[2]} #{name_split[3]}")
  else
    return nil
  end

  { conditions: conditions, values: values }
end

# bundle exec rake 'nyccoba_terminated_dependents_update[nyccoba, dependents.csv]'
# bundle exec rake 'nyccoba_employees_of_terminated_dependents[nyccoba, terminated_dependents_update07-01-2025-13_39 (1).csv]'
