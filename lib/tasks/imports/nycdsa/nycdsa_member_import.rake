# frozen_string_literal: true

require 'csv'

desc 'import data'

task :nycdsa_member_import, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' }
  ]

  rank = Rank.where('lower(name) = ?', 'cadet').first_or_create!(name: 'Cadet')
  csv_file.each do |row|
    first_name = row['FIRST NAME'] || ''
    last_name = row['LAST NAME'] || ''
    @row_number = [first_name, last_name]
    if first_name.blank? || last_name.blank?
      next if (@row_number = row['DOB']).blank?

      nycdsa_feed_errors('Mandatory details not present')
      next
    end
    dob = Date.strptime(row['DOB'], '%m/%d/%Y') if row['DOB'].present?
    gender = Gender.where('lower(name) = ?', row['GENDER'].downcase).first_or_create!(name: row['GENDER'].strip) if row['GENDER'].present?
    hire_date = Date.strptime(row['HIRE DATE'], '%m/%d/%Y') if row['HIRE DATE'].present?
    social_security_number = parse_ssn(row['SOCIAL SECURITY']) if row['SOCIAL SECURITY'].present?
    employee_id = row['EMPLOYEE ID'] || ''
    phone = row['PHONE']
    street = row['STREET ADDRESS'] || ''
    apt = row['APT'] || ''
    city = row['CITY'] || ''
    state = row['STATE'] || ''
    zipcode = row['ZIP CODE'] || ''
    email = row['EMAIL ADDRESS'] || ''
    union_agreement_date = Date.strptime(row['UNION AGREEMENT DATE'], '%m/%d/%Y') if row['UNION AGREEMENT DATE']
    emblem_health = row['EMBLEM HEALTH ID NUMBER'] || ''

    employees = Employee.kept.where("lower(first_name) = ? and lower(last_name) = ?", first_name.downcase.strip, last_name.downcase.strip)
    if employees.count > 1 && dob.present?
      employees = employees.where(birthday: dob)
      if employees.count > 1
        nycdsa_feed_errors('More than One Employee found')
        next
      end
    end

    if employees.blank? || employees.count == 1
      employee = if employees.blank?
                   Employee.new
                 else
                   employees.first
                 end
      employee.first_name = first_name.strip
      employee.last_name = last_name.strip
      employee.birthday = dob
      employee.gender_id = gender.id if gender
      employee.start_date = hire_date
      employee.social_security_number = social_security_number if social_security_number.present?
      employee.title_code = employee_id
      employee.street = street
      employee.apartment = apt
      employee.city = city
      employee.state = state
      employee.zipcode = zipcode
      employee.member_since = union_agreement_date
      employee.a_number = emblem_health
      if employee.new_record? && employee.save!
        employee.contacts.import contacts_hash
      else
        employee.save!
      end

      employee.employee_ranks.first_or_create!(rank_id: rank.id).save!
      phone_number = employee.contacts.where(contact_type: 'phone', contact_for: 'personal').first
      email_contact = employee.contacts.where(contact_type: 'email', contact_for: 'personal').first
      phone_number.update_columns(value: parse_phone(phone)) if phone.present?

      email_contact.update_columns(value: email.strip) if email.present?
    end

  rescue => e
    p @row_number, e.message
    nycdsa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_import_errors_#{Time.now.strftime('%Y%m%d%H%M%S')}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

def nycdsa_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake "nycdsa_member_import[nycdsa, Sheet1-Table 1.csv]"
